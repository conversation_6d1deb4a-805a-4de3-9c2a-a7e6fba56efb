package com.ms.infinity.platform.customers.application.service;

import com.ms.infinity.platform.customers.application.port.input.client.ClientUseCase;
import com.ms.infinity.platform.customers.application.port.output.ClientRepository;
import com.ms.infinity.platform.customers.domain.model.Client;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityNotFoundException;
import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implementación del caso de uso de clientes.
 * Conecta los puertos de entrada con los de salida para ejecutar la lógica de negocio.
 */
@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class ClientService implements ClientUseCase {

    private final ClientRepository clientRepository;

    @Override
    public List<Client> getAllClients() {
        log.info("Obteniendo todos los clientes");
        return clientRepository.findAll();
    }

    @Override
    public Optional<Client> getClientById(Integer id) {
        log.info("Buscando cliente con ID: {}", id);
        return clientRepository.findById(id);
    }

    @Override
    public List<Client> searchClientsByCompanyName(String companyName) {
        log.info("Buscando clientes por nombre de empresa: {}", companyName);
        return clientRepository.findByCompanyNameContaining(companyName);
    }

    @Override
    @Transactional
    public Client createClient(Client client) {
        log.info("Creando nuevo cliente: {}", client.getCompanyName());
        client.setDefaultValues();

        // Validación de negocio
        if (!client.canBeActivated()) {
            throw new IllegalArgumentException("El cliente no puede ser creado con datos incompletos");
        }

        return clientRepository.save(client);
    }

    @Override
    @Transactional
    public Client updateClient(Integer id, Client client) {
        log.info("Actualizando cliente con ID: {}", id);

        // Verificar que el cliente existe
        if (!clientRepository.existsById(id)) {
            throw new EntityNotFoundException("Cliente no encontrado con ID: " + id);
        }

        // Asegurar que el ID sea el correcto
        client.setClientId(id);

        // Establecer la fecha de actualización
        client.setUpdateDate(LocalDateTime.now());

        return clientRepository.update(client);
    }

    @Override
    @Transactional
    public Client changeClientStatus(Integer id, boolean active) {
        log.info("Cambiando estado del cliente ID {} a {}", id, active ? "activo" : "inactivo");

        Client client = clientRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Cliente no encontrado con ID: " + id));

        client.setIsActive(active);

        // Establecer la fecha de actualización
        client.setUpdateDate(LocalDateTime.now());

        return clientRepository.update(client);
    }

    @Override
    @Transactional
    public void deleteClient(Integer id) {
        log.info("Eliminando cliente con ID: {}", id);

        if (!clientRepository.existsById(id)) {
            throw new EntityNotFoundException("Cliente no encontrado con ID: " + id);
        }

        boolean deleted = clientRepository.deleteById(id);
        if (!deleted) {
            throw new RuntimeException("No se pudo eliminar el cliente con ID: " + id);
        }
    }
}