package com.ms.infinity.platform.customers.infrastructure.adapter.input.rest;

import com.ms.infinity.platform.customers.application.port.input.client.ClientUseCase;
import com.ms.infinity.platform.customers.domain.model.Client;
import com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.client.CreateClientDTO;
import com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.client.UpdateClientDTO;
import com.ms.infinity.platform.customers.infrastructure.configuration.Constants;
import com.ms.infinity.platform.customers.infrastructure.exception.ClientException;
import com.ms.infinity.platform.customers.infrastructure.security.annotation.RoleSecured;
import com.ms.infinity.platform.customers.infrastructure.util.HttpUtils;

import jakarta.annotation.security.PermitAll;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import java.util.Optional;

/**
 * Controlador REST para operaciones CRUD sobre clientes.
 * Actúa como adaptador de entrada para el puerto ClientUseCase.
 */
@Path(Constants.Path.CLIENT_BASE)
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Tag(name = "Client", description = "Client management operations")
@ApplicationScoped
public class ClientResource {

    @Inject
    ClientUseCase clientUseCase;

    @Inject
    HttpUtils httpUtils;

    /**
     * Crea un nuevo cliente en el sistema.
     *
     * @param dto Datos del cliente a crear
     * @param token Token de autenticación
     * @return Cliente creado
     */
    @POST
    @Operation(summary = Constants.Operation.CLIENT_CREATE)
    @PermitAll
    public Response createClient(
            @Valid CreateClientDTO dto,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            Client client = new Client();
            client.setCompanyName(dto.companyName());
            client.setTaxId(dto.taxId());
            client.setBusinessName(dto.businessName());
            client.setVerificationNumber(dto.verificationNumber());
            client.setContactName(dto.contactName());
            client.setAddress(dto.address());
            client.setCountryCode(dto.countryCode());
            client.setPhone(dto.phone());
            client.setEmail(dto.email());
            client.setBirthDate(dto.birthDate());
            client.setVatRate(dto.vatRate());
            client.setConsumptionTaxRate(dto.consumptionTaxRate());
            client.setAllowedUsers(dto.allowedUsers());
            client.setUserId(dto.userId());
            client.setIsActive(true);

            Client createdClient = clientUseCase.createClient(client);
            return httpUtils.buildSuccessResponse(
                    createdClient,
                    Constants.Success.CLIENT_CREATED,
                    Response.Status.CREATED.getStatusCode()
            );
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.VALIDATION,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_CREATE,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Obtiene un cliente por su ID.
     *
     * @param id ID del cliente
     * @param token Token de autenticación
     * @return Cliente solicitado o error si no existe
     */
    @GET
    @Path(Constants.Path.ID)
    @Operation(summary = Constants.Operation.CLIENT_GET_BY_ID)
    @Transactional
    @PermitAll
    public Response getClientById(
            @PathParam("id") Integer id,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            return clientUseCase.getClientById(id)
                    .map(client -> httpUtils.buildSuccessResponse(
                            client,
                            Constants.Success.CLIENT_FOUND,
                            Response.Status.OK.getStatusCode()
                    ))
                    .orElseThrow(() -> new ClientException(Constants.Error.CLIENT_NOT_FOUND + id));
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.NOT_FOUND,
                    e.getMessage(),
                    Response.Status.NOT_FOUND.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_FIND,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Obtiene todos los clientes del sistema.
     *
     * @param token Token de autenticación
     * @return Lista de clientes
     */
    @GET
    @Operation(summary = Constants.Operation.CLIENT_GET_ALL)
    @Transactional
    @RoleSecured("administrador")
    public Response getAllClients(
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            var clients = clientUseCase.getAllClients();
            return httpUtils.buildSuccessResponse(
                    clients,
                    Constants.Success.CLIENT_RETRIEVED,
                    Response.Status.OK.getStatusCode()
            );
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_LIST,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Busca clientes por nombre de empresa.
     *
     * @param companyName Nombre o parte del nombre de la empresa
     * @param token Token de autenticación
     * @return Lista de clientes que coinciden con el criterio
     */
    @GET
    @Path("/search")
    @Operation(summary = Constants.Operation.CLIENT_SEARCH)
    @Transactional
    @PermitAll
    public Response searchClientsByCompanyName(
            @QueryParam("companyName") String companyName,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            var clients = clientUseCase.searchClientsByCompanyName(companyName);
            return httpUtils.buildSuccessResponse(
                    clients,
                    Constants.Success.CLIENT_RETRIEVED,
                    Response.Status.OK.getStatusCode()
            );
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.RETRIEVING,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_SEARCH,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }

    /**
     * Actualiza los datos de un cliente existente.
     *
     * @param id ID del cliente a actualizar
     * @param dto Nuevos datos del cliente
     * @param token Token de autenticación
     * @return Cliente actualizado
     */
    @PUT
    @Path(Constants.Path.ID)
    @Operation(summary = Constants.Operation.CLIENT_UPDATE)
    @Transactional
    @PermitAll
    public Response updateClient(
            @PathParam("id") Integer id,
            @Valid UpdateClientDTO dto,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            // Primero verificar que el cliente existe
            Optional<Client> existingClient = clientUseCase.getClientById(id);
            if (existingClient.isEmpty()) {
                throw new ClientException(Constants.Error.CLIENT_NOT_FOUND + id);
            }

            Client client = new Client();
            client.setClientId(id);
            client.setCompanyName(dto.companyName());
            client.setTaxId(dto.taxId());
            client.setBusinessName(dto.businessName());
            client.setVerificationNumber(dto.verificationNumber());
            client.setContactName(dto.contactName());
            client.setAddress(dto.address());
            client.setCountryCode(dto.countryCode());
            client.setPhone(dto.phone());
            client.setEmail(dto.email());
            client.setBirthDate(dto.birthDate());
            client.setVatRate(dto.vatRate());
            client.setConsumptionTaxRate(dto.consumptionTaxRate());
            client.setAllowedUsers(dto.allowedUsers());
            client.setIsActive(dto.isActive());
            // El updateDate se establece automáticamente en el servicio

            // Mantener los valores originales de estos campos
            client.setRegistrationDate(existingClient.get().getRegistrationDate());
            client.setUserId(existingClient.get().getUserId());

            Client updatedClient = clientUseCase.updateClient(id, client);
            return httpUtils.buildSuccessResponse(
                    updatedClient,
                    Constants.Success.CLIENT_UPDATED,
                    Response.Status.OK.getStatusCode()
            );
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.VALIDATION,
                    e.getMessage(),
                    Response.Status.BAD_REQUEST.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_UPDATE,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }



    /**
     * Elimina un cliente del sistema.
     *
     * @param id ID del cliente a eliminar
     * @param token Token de autenticación
     * @return Confirmación de la operación
     */
    @DELETE
    @Path(Constants.Path.ID)
    @Operation(summary = Constants.Operation.CLIENT_DELETE)
    @PermitAll
    public Response deleteClient(
            @PathParam("id") Integer id,
            @HeaderParam(Constants.Header.TOKEN)
            @Parameter(description = Constants.Header.TOKEN_DESCRIPTION, required = true)
            String token) {
        try {
            clientUseCase.deleteClient(id);
            return httpUtils.buildSuccessResponse(
                    null,
                    Constants.Success.CLIENT_DELETED,
                    Response.Status.OK.getStatusCode()
            );
        } catch (ClientException e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.NOT_FOUND,
                    e.getMessage(),
                    Response.Status.NOT_FOUND.getStatusCode()
            );
        } catch (Exception e) {
            return httpUtils.buildErrorResponse(
                    Constants.Error.INTERNAL,
                    Constants.Error.CLIENT_INTERNAL_DELETE,
                    Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()
            );
        }
    }
}