package com.ms.infinity.platform.customers.domain.model;

import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Objeto de dominio que representa un cliente en el sistema.
 * Este modelo es independiente de la persistencia y contiene
 * la lógica de negocio relacionada con los clientes.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Client {
    private Integer clientId;
    private String companyName;
    private String taxId;
    private String businessName;
    private String verificationNumber;
    private String contactName;
    private String address;
    private String countryCode;  // Nuevo campo
    private String phone;
    private String email;
    private LocalDate birthDate;
    private LocalDateTime registrationDate;
    private BigDecimal vatRate;
    private BigDecimal consumptionTaxRate;
    private Integer allowedUsers;

    private Boolean isActive;

    private Long userId;  // Nuevo campo - FK → users.id
    private LocalDateTime updateDate;  // Nuevo campo

    /**
     * Método que valida si el cliente puede estar activo
     * en función de sus datos obligatorios.
     *
     * @return true si el cliente puede activarse, false en caso contrario
     */
    public boolean canBeActivated() {
        return companyName != null && !companyName.isBlank()
                && taxId != null && !taxId.isBlank()
                && email != null && !email.isBlank();
    }

    /**
     * Método que establece valores por defecto para nuevos clientes
     */
    public void setDefaultValues() {
        if (registrationDate == null) {
            registrationDate = LocalDateTime.now();
        }

        if (vatRate == null) {
            vatRate = BigDecimal.ZERO;
        }

        if (consumptionTaxRate == null) {
            consumptionTaxRate = BigDecimal.ZERO;
        }

        if (allowedUsers == null) {
            allowedUsers = 1;
        }

        if (isActive == null) {
            isActive = true;
        }
    }

    // Métodos para controlar la serialización JSON y evitar duplicación
    @JsonGetter("isActive")
    public Boolean getIsActiveForJson() {
        return isActive;
    }

    @JsonSetter("isActive")
    public void setIsActiveFromJson(Boolean isActive) {
        this.isActive = isActive;
    }

    // Ignorar los getters automáticos de Lombok que pueden causar duplicación
    @JsonIgnore
    public Boolean getIsActive() {
        return isActive;
    }

    @JsonIgnore
    public boolean isActive() {
        return Boolean.TRUE.equals(isActive);
    }
}