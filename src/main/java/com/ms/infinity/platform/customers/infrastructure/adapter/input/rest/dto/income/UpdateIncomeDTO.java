package com.ms.infinity.platform.customers.infrastructure.adapter.input.rest.dto.income;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * DTO para la actualización de un ingreso existente.
 */
public record UpdateIncomeDTO(
    @NotNull(message = "El monto es obligatorio")
    @Positive(message = "El monto debe ser mayor que cero")
    BigDecimal amount,

    @NotNull(message = "El tipo de ingreso es obligatorio")
    Integer incomeTypeId,

    @NotNull(message = "El usuario es obligatorio")
    Long userId,

    Integer clientId,

    LocalDateTime date,

    String description,

    LocalDateTime updateDate
) {}